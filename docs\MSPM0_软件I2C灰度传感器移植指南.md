# MSPM0 软件I2C灰度传感器移植指南

## 📋 移植文件清单

### 🔧 核心移植文件（必需）

| 文件名 | 路径 | 功能描述 | 移植要求 |
|--------|------|----------|----------|
| `software_iic.h` | `User/` | 软件I2C头文件 | **必需移植** |
| `software_iic.c` | `User/` | 软件I2C实现 | **必需移植** |
| `gw_grayscale_sensor.h` | `User/` | 灰度传感器寄存器定义 | **必需移植** |
| `No_Mcu_Ganv_Grayscale_Sensor.c` | `User/` | 无MCU灰度传感器驱动 | **必需移植** |
| `No_Mcu_Ganv_Grayscale_Sensor_Config.h` | `User/` | 传感器配置文件 | **必需移植** |

### 🛠️ 依赖文件（BSP层）

| 文件名 | 路径 | 功能描述 | 移植要求 |
|--------|------|----------|----------|
| `Time.h/.c` | `BSP/` | 延时函数 | **必需适配** |
| `ADC.h/.c` | `BSP/` | ADC采样函数 | **必需适配** |
| `ti_msp_dl_config.h` | 根目录 | GPIO配置文件 | **必需配置** |

## 🔌 硬件连接要求

### I2C通信引脚
```c
// 在ti_msp_dl_config.h中定义
#define Software_iic_PORT       GPIOA        // I2C端口
#define Software_iic_SDA_PIN    DL_GPIO_PIN_0 // SDA引脚
#define Software_iic_SCL_PIN    DL_GPIO_PIN_1 // SCL引脚
#define Software_iic_SDA_IOMUX  GPIO_PA0_IOMUX // SDA复用配置
```

### 无MCU版本额外引脚（如使用）
```c
// 地址选择引脚（3位地址线）
#define Gray_Address_PORT       GPIOA
#define Gray_Address_PIN_0_PIN  DL_GPIO_PIN_2  // 地址位0
#define Gray_Address_PIN_1_PIN  DL_GPIO_PIN_3  // 地址位1  
#define Gray_Address_PIN_2_PIN  DL_GPIO_PIN_4  // 地址位2

// ADC输入引脚
#define ADC_INPUT_PIN           // 根据实际ADC通道配置
```

## 🎯 循迹关键量

### 数字量输出（循迹核心）
```c
// 8位数字量，每位代表一个传感器
unsigned char digital_value = Get_Digtal_For_User(&sensor);

// 提取单个传感器状态
#define GET_SENSOR_BIT(value, n) (((value) >> ((n)-1)) & 0x01)

// 循迹判断示例
if(digital_value & 0x18) {      // 中间两个传感器检测到线
    // 直行
} else if(digital_value & 0x07) { // 左侧传感器检测到线
    // 左转
} else if(digital_value & 0xE0) { // 右侧传感器检测到线  
    // 右转
}
```

### 模拟量输出（精确控制）
```c
unsigned short analog_values[8];    // 原始ADC值
unsigned short normal_values[8];    // 归一化值(0-4095)

// 获取归一化数据用于PID控制
Get_Normalize_For_User(&sensor, normal_values);

// 计算循迹偏差（加权平均法）
int line_position = 0;
int total_weight = 0;
for(int i = 0; i < 8; i++) {
    line_position += normal_values[i] * (i - 3.5) * 1000;
    total_weight += normal_values[i];
}
if(total_weight > 0) {
    line_position = line_position / total_weight; // 偏差值
}
```

## 🚀 关键API接口

### 初始化API
```c
// 1. 首次初始化（清零所有参数）
void No_MCU_Ganv_Sensor_Init_Frist(No_MCU_Sensor* sensor);

// 2. 带校准参数初始化（推荐）
void No_MCU_Ganv_Sensor_Init(No_MCU_Sensor* sensor, 
                             unsigned short* white_values,
                             unsigned short* black_values);
```

### 数据获取API
```c
// 获取8位数字量（循迹主要使用）
unsigned char Get_Digtal_For_User(No_MCU_Sensor* sensor);

// 获取归一化模拟量（PID控制使用）
unsigned char Get_Normalize_For_User(No_MCU_Sensor* sensor, 
                                    unsigned short* result);

// 获取原始ADC值（调试使用）
unsigned char Get_Anolog_Value(No_MCU_Sensor* sensor, 
                              unsigned short* result);
```

### 任务处理API
```c
// 无定时器版本（简单轮询）
void No_Mcu_Ganv_Sensor_Task_Without_tick(No_MCU_Sensor* sensor);

// 有定时器版本（节省CPU）
void No_Mcu_Ganv_Sensor_Task_With_tick(No_MCU_Sensor* sensor);
void Task_tick(No_MCU_Sensor* sensor); // 1ms定时器调用
```

### I2C通信API
```c
// 传感器连接检测
unsigned char Ping(void); // 返回0表示连接正常

// I2C读写操作
uint8_t IIC_ReadBytes(uint8_t slave_addr, uint8_t reg_addr, 
                     uint8_t* data, uint8_t len);
uint8_t IIC_WriteByte(uint8_t slave_addr, uint8_t reg_addr, 
                     uint8_t data);

// 传感器重置
void i2c_reset(void); // 广播重置所有传感器地址
```

## ⚙️ 配置参数

### 传感器版本配置
```c
#define Class   0  // 经典版传感器
#define Younth  1  // 青春版传感器
#define Sensor_Edition Class  // 选择版本
```

### ADC分辨率配置
```c
#define _12Bits 1     // 12位ADC（推荐）
#define _10Bits 2     // 10位ADC
#define _8Bits  3     // 8位ADC
#define Sensor_ADCbits _12Bits
```

### 方向配置
```c
#define Direction 1   // 输出方向，与预期不同时设为1
```

## 📝 移植适配要点

### 1. GPIO配置适配
```c
// 需要在ti_msp_dl_config.h中正确配置I2C引脚
// 确保引脚复用功能正确设置为GPIO模式
```

### 2. 延时函数适配
```c
// Time.c中实现
void delay_us(unsigned long us);  // 微秒延时
void delay_ms(unsigned long ms);  // 毫秒延时
```

### 3. ADC函数适配
```c
// ADC.c中实现
unsigned int adc_getValue(void);  // 返回ADC采样值
```

### 4. 中断优先级配置
```c
// 如使用定时器版本，需配置1ms定时器中断
// 优先级应低于关键系统中断
```

## 🔍 调试建议

### 1. 硬件连接检查
- 使用`Ping()`函数检测传感器连接
- 检查I2C时序是否正确（示波器）
- 验证电源供电稳定性

### 2. 软件调试步骤
- 先测试I2C基本读写功能
- 验证ADC采样是否正常
- 检查校准参数是否合理
- 观察数字量输出变化

### 3. 性能优化
- 根据应用需求选择合适的ADC分辨率
- 调整采样频率平衡精度与速度
- 使用定时器版本减少CPU占用

## 🎮 完整工作流示例（MSPM0）

### 主程序示例
```c
#include "ti_msp_dl_config.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "software_iic.h"

// 全局变量
No_MCU_Sensor grayscale_sensor;
unsigned short white_calibration[8] = {3000,3000,3000,3000,3000,3000,3000,3000};
unsigned short black_calibration[8] = {500,500,500,500,500,500,500,500};

int main(void)
{
    // 1. 系统初始化
    SYSCFG_DL_init();

    // 2. 传感器初始化
    No_MCU_Ganv_Sensor_Init(&grayscale_sensor, white_calibration, black_calibration);

    // 3. 检测传感器连接
    if(Ping() == 0) {
        // 传感器连接正常
        printf("Grayscale sensor connected!\n");
    } else {
        // 连接失败，尝试重置
        i2c_reset();
        delay_ms(100);
    }

    // 4. 主循环
    while(1) {
        // 执行传感器任务
        No_Mcu_Ganv_Sensor_Task_Without_tick(&grayscale_sensor);

        // 获取循迹数据
        unsigned char digital_line = Get_Digtal_For_User(&grayscale_sensor);

        // 循迹控制逻辑
        line_following_control(digital_line);

        delay_ms(10); // 100Hz采样频率
    }
}

// 循迹控制函数
void line_following_control(unsigned char line_data)
{
    // 基于8位数字量的循迹算法
    if(line_data == 0x18) {        // 00011000 - 中间检测到线
        motor_forward();           // 直行
    }
    else if(line_data & 0x07) {    // 00000111 - 左侧检测到线
        motor_turn_right();        // 右转修正
    }
    else if(line_data & 0xE0) {    // 11100000 - 右侧检测到线
        motor_turn_left();         // 左转修正
    }
    else if(line_data == 0x00) {   // 00000000 - 丢线
        motor_search_line();       // 寻线
    }
    else {
        motor_forward();           // 默认直行
    }
}
```

### 高精度PID循迹示例
```c
// PID控制参数
float kp = 2.0, ki = 0.1, kd = 0.5;
float last_error = 0, integral = 0;

void advanced_line_following(void)
{
    unsigned short normal_values[8];

    // 获取归一化数据
    if(Get_Normalize_For_User(&grayscale_sensor, normal_values)) {

        // 计算加权位置（-3500到+3500）
        float line_position = calculate_line_position(normal_values);

        // PID控制
        float error = line_position;
        integral += error;
        float derivative = error - last_error;

        float pid_output = kp * error + ki * integral + kd * derivative;

        // 限幅
        if(pid_output > 100) pid_output = 100;
        if(pid_output < -100) pid_output = -100;

        // 电机控制
        int left_speed = 50 - pid_output;
        int right_speed = 50 + pid_output;

        motor_set_speed(left_speed, right_speed);

        last_error = error;
    }
}

float calculate_line_position(unsigned short* values)
{
    float weighted_sum = 0;
    float total_weight = 0;

    for(int i = 0; i < 8; i++) {
        float weight = values[i] / 4095.0; // 归一化到0-1
        weighted_sum += weight * (i - 3.5) * 1000;
        total_weight += weight;
    }

    if(total_weight > 0.1) {
        return weighted_sum / total_weight;
    }
    return 0; // 丢线情况
}
```

### 定时器版本示例
```c
// 使用定时器版本（推荐用于多任务系统）
void timer_1ms_interrupt(void)
{
    // 在1ms定时器中断中调用
    Task_tick(&grayscale_sensor);
}

int main(void)
{
    SYSCFG_DL_init();
    No_MCU_Ganv_Sensor_Init(&grayscale_sensor, white_calibration, black_calibration);

    // 启动1ms定时器
    start_1ms_timer();

    while(1) {
        // 非阻塞任务处理
        No_Mcu_Ganv_Sensor_Task_With_tick(&grayscale_sensor);

        // 其他任务处理
        other_tasks();

        // 可以进入低功耗模式
        __WFI();
    }
}
```

## 📊 性能参数参考

| 参数 | 典型值 | 说明 |
|------|--------|------|
| I2C时钟频率 | 100kHz | 标准I2C速度 |
| 采样频率 | 100Hz | 循迹应用推荐 |
| ADC分辨率 | 12位 | 平衡精度与速度 |
| 响应时间 | <10ms | 从采样到输出 |
| 功耗 | <50mA | 包含传感器功耗 |

## ⚠️ 注意事项

1. **电源稳定性**：确保3.3V供电稳定，纹波<50mV
2. **I2C上拉电阻**：SDA/SCL需要4.7kΩ上拉电阻
3. **EMI防护**：I2C线路远离高频信号源
4. **校准重要性**：必须在实际使用环境中进行黑白校准
5. **定时器精度**：1ms定时器误差应<1%
