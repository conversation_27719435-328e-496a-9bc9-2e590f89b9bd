Dependencies for Project 'gpio_toggle_output_LP_MSPM0G3507_nortos_keil', Target 'gpio_toggle_output_LP_MSPM0G3507_nortos_keil': (DO NOT MODIFY !)
CompilerVersion: 6210000::V6.21::ARMCLANG
F (../gpio_toggle_output.c)(0x687F5AD9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./source/third_party/CMSIS/Core/Include -I ./source -I ./BSP -I ./User -I ../../Software_IIC

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/gpio_toggle_output.o -MD)
I (..\ti_msp_dl_config.h.ource\ti\devices\msp\msp.h)(0x00000000)
I (source\ti\devices\DeviceFamily.h)(0x65B1D8C1)
I (source\ti\devices\msp\m0p\mspm0g350x.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_adc12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_aes.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_comp.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_crc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dac12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dma.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_flashctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gpio.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gptimer.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_i2c.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_iomux.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mathacl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mcan.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_oa.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_rtc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_spi.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_trng.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_uart.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_vref.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wuc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wwdt.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\driverlib.hlource\ti\driverlib\dl_adc12.h)(0x00000000)
I (source\ti\driverlib\dl_common.hlource\ti\driverlib\dl_aes.h)(0x00000000)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (source\ti\driverlib\dl_aesadv.hcource\ti\driverlib\dl_comp.h)(0x00000000)
I (source\ti\driverlib\dl_crc.hvource\ti\driverlib\dl_crcp.h)(0x00000000)
I (source\ti\driverlib\dl_dac12.huource\ti\driverlib\dl_dma.h)(0x00000000)
I (source\ti\driverlib\dl_flashctl.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_factoryregion.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_core.hyource\ti\driverlib\m0p\dl_sysctl.h)(0x00000000)
I (source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\dl_gpamp.h\ource\ti\driverlib\dl_gpio.h)(0x00000000)
I (source\ti\driverlib\dl_i2c.h.ource\ti\driverlib\dl_iwdt.h)(0x00000000)
I (source\ti\driverlib\dl_lfss.hoource\ti\driverlib\dl_keystorectl.h)(0x00000000)
I (source\ti\driverlib\dl_lcd.hhource\ti\driverlib\dl_mathacl.h)(0x00000000)
I (source\ti\driverlib\dl_mcan.hoource\ti\driverlib\dl_opa.h)(0x00000000)
I (source\ti\driverlib\dl_rtc.hhource\ti\driverlib\dl_rtc_common.h)(0x00000000)
I (source\ti\driverlib\dl_rtc_a.huource\ti\driverlib\dl_scratchpad.h)(0x00000000)
I (source\ti\driverlib\dl_spi.h.ource\ti\driverlib\dl_tamperio.h)(0x00000000)
I (source\ti\driverlib\dl_timera.hrource\ti\driverlib\dl_timer.h)(0x00000000)
I (source\ti\driverlib\dl_timerg.hrource\ti\driverlib\dl_trng.h)(0x00000000)
I (source\ti\driverlib\dl_uart_extend.heource\ti\driverlib\dl_uart.h)(0x00000000)
I (source\ti\driverlib\dl_uart_main.h.ource\ti\driverlib\dl_vref.h)(0x00000000)
I (source\ti\driverlib\dl_wwdt.haource\ti\driverlib\m0p\dl_interrupt.h)(0x00000000)
I (source\ti\driverlib\m0p\dl_systick.htSP\Time.h)(0x00000000)
I (..\..\Software_IIC\ti_msp_dl_config.h)(0x67F619D8)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdio.h.SP\Uart.h)(0x00000000)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\string.hSser\software_iic.h)(0x00000000)
I (User\gw_color_sensor.h)(0x682AC7BB)
F (../gpio_toggle_output.syscfg)(0x67E105D8)()
F (startup_mspm0g350x_uvision.s)(0x67D7C841)(--target=arm-arm-none-eabi -mcpu=cortex-m0plus -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-Wa,armasm,--pd,"__UVISION_VERSION SETA 539" -Wa,armasm,--pd,"__MSPM0G3507__ SETA 1"

-o ./objects/startup_mspm0g350x_uvision.o)
F (../ti_msp_dl_config.h)(0x67F619D8)()
F (../ti_msp_dl_config.c)(0x67F619D8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./source/third_party/CMSIS/Core/Include -I ./source -I ./BSP -I ./User -I ../../Software_IIC

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MD)
I (source\ti\devices\msp\msp.hmource\ti\devices\DeviceFamily.h)(0x00000000)
I (source\ti\devices\msp\m0p\mspm0g350x.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_adc12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_aes.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_comp.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_crc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dac12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dma.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_flashctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gpio.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gptimer.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_i2c.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_iomux.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mathacl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mcan.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_oa.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_rtc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_spi.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_trng.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_uart.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_vref.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wuc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wwdt.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\driverlib.hlource\ti\driverlib\dl_adc12.h)(0x00000000)
I (source\ti\driverlib\dl_common.hlource\ti\driverlib\dl_aes.h)(0x00000000)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (source\ti\driverlib\dl_aesadv.hcource\ti\driverlib\dl_comp.h)(0x00000000)
I (source\ti\driverlib\dl_crc.hvource\ti\driverlib\dl_crcp.h)(0x00000000)
I (source\ti\driverlib\dl_dac12.huource\ti\driverlib\dl_dma.h)(0x00000000)
I (source\ti\driverlib\dl_flashctl.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_factoryregion.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_core.hyource\ti\driverlib\m0p\dl_sysctl.h)(0x00000000)
I (source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\dl_gpamp.h\ource\ti\driverlib\dl_gpio.h)(0x00000000)
I (source\ti\driverlib\dl_i2c.h.ource\ti\driverlib\dl_iwdt.h)(0x00000000)
I (source\ti\driverlib\dl_lfss.hoource\ti\driverlib\dl_keystorectl.h)(0x00000000)
I (source\ti\driverlib\dl_lcd.hhource\ti\driverlib\dl_mathacl.h)(0x00000000)
I (source\ti\driverlib\dl_mcan.hoource\ti\driverlib\dl_opa.h)(0x00000000)
I (source\ti\driverlib\dl_rtc.hhource\ti\driverlib\dl_rtc_common.h)(0x00000000)
I (source\ti\driverlib\dl_rtc_a.huource\ti\driverlib\dl_scratchpad.h)(0x00000000)
I (source\ti\driverlib\dl_spi.h.ource\ti\driverlib\dl_tamperio.h)(0x00000000)
I (source\ti\driverlib\dl_timera.hrource\ti\driverlib\dl_timer.h)(0x00000000)
I (source\ti\driverlib\dl_timerg.hrource\ti\driverlib\dl_trng.h)(0x00000000)
I (source\ti\driverlib\dl_uart_extend.heource\ti\driverlib\dl_uart.h)(0x00000000)
I (source\ti\driverlib\dl_uart_main.h.ource\ti\driverlib\dl_vref.h)(0x00000000)
I (source\ti\driverlib\dl_wwdt.haource\ti\driverlib\m0p\dl_interrupt.h)(0x00000000)
I (source\ti\driverlib\m0p\dl_systick.h)(0x65B1D8C1)
F (.\BSP\Time.c)(0x67D8DBCA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./source/third_party/CMSIS/Core/Include -I ./source -I ./BSP -I ./User -I ../../Software_IIC

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/time.o -MD)
I (..\..\Software_IIC\ti_msp_dl_config.hiource\ti\devices\msp\msp.h)(0x00000000)
I (source\ti\devices\DeviceFamily.h)(0x65B1D8C1)
I (source\ti\devices\msp\m0p\mspm0g350x.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_adc12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_aes.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_comp.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_crc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dac12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dma.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_flashctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gpio.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gptimer.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_i2c.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_iomux.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mathacl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mcan.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_oa.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_rtc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_spi.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_trng.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_uart.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_vref.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wuc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wwdt.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\driverlib.hlource\ti\driverlib\dl_adc12.h)(0x00000000)
I (source\ti\driverlib\dl_common.hlource\ti\driverlib\dl_aes.h)(0x00000000)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (source\ti\driverlib\dl_aesadv.hcource\ti\driverlib\dl_comp.h)(0x00000000)
I (source\ti\driverlib\dl_crc.hvource\ti\driverlib\dl_crcp.h)(0x00000000)
I (source\ti\driverlib\dl_dac12.huource\ti\driverlib\dl_dma.h)(0x00000000)
I (source\ti\driverlib\dl_flashctl.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_factoryregion.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_core.hyource\ti\driverlib\m0p\dl_sysctl.h)(0x00000000)
I (source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\dl_gpamp.h\ource\ti\driverlib\dl_gpio.h)(0x00000000)
I (source\ti\driverlib\dl_i2c.h.ource\ti\driverlib\dl_iwdt.h)(0x00000000)
I (source\ti\driverlib\dl_lfss.hoource\ti\driverlib\dl_keystorectl.h)(0x00000000)
I (source\ti\driverlib\dl_lcd.hhource\ti\driverlib\dl_mathacl.h)(0x00000000)
I (source\ti\driverlib\dl_mcan.hoource\ti\driverlib\dl_opa.h)(0x00000000)
I (source\ti\driverlib\dl_rtc.hhource\ti\driverlib\dl_rtc_common.h)(0x00000000)
I (source\ti\driverlib\dl_rtc_a.huource\ti\driverlib\dl_scratchpad.h)(0x00000000)
I (source\ti\driverlib\dl_spi.h.ource\ti\driverlib\dl_tamperio.h)(0x00000000)
I (source\ti\driverlib\dl_timera.hrource\ti\driverlib\dl_timer.h)(0x00000000)
I (source\ti\driverlib\dl_timerg.hrource\ti\driverlib\dl_trng.h)(0x00000000)
I (source\ti\driverlib\dl_uart_extend.heource\ti\driverlib\dl_uart.h)(0x00000000)
I (source\ti\driverlib\dl_uart_main.h.ource\ti\driverlib\dl_vref.h)(0x00000000)
I (source\ti\driverlib\dl_wwdt.haource\ti\driverlib\m0p\dl_interrupt.h)(0x00000000)
I (source\ti\driverlib\m0p\dl_systick.h)(0x65B1D8C1)
F (.\BSP\Uart.c)(0x67D8DB01)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./source/third_party/CMSIS/Core/Include -I ./source -I ./BSP -I ./User -I ../../Software_IIC

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/uart.o -MD)
I (..\..\Software_IIC\ti_msp_dl_config.hiource\ti\devices\msp\msp.h)(0x00000000)
I (source\ti\devices\DeviceFamily.h)(0x65B1D8C1)
I (source\ti\devices\msp\m0p\mspm0g350x.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_adc12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_aes.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_comp.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_crc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dac12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dma.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_flashctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gpio.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gptimer.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_i2c.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_iomux.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mathacl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mcan.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_oa.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_rtc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_spi.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_trng.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_uart.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_vref.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wuc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wwdt.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\driverlib.hlource\ti\driverlib\dl_adc12.h)(0x00000000)
I (source\ti\driverlib\dl_common.hlource\ti\driverlib\dl_aes.h)(0x00000000)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (source\ti\driverlib\dl_aesadv.hcource\ti\driverlib\dl_comp.h)(0x00000000)
I (source\ti\driverlib\dl_crc.hvource\ti\driverlib\dl_crcp.h)(0x00000000)
I (source\ti\driverlib\dl_dac12.huource\ti\driverlib\dl_dma.h)(0x00000000)
I (source\ti\driverlib\dl_flashctl.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_factoryregion.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_core.hyource\ti\driverlib\m0p\dl_sysctl.h)(0x00000000)
I (source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\dl_gpamp.h\ource\ti\driverlib\dl_gpio.h)(0x00000000)
I (source\ti\driverlib\dl_i2c.h.ource\ti\driverlib\dl_iwdt.h)(0x00000000)
I (source\ti\driverlib\dl_lfss.hoource\ti\driverlib\dl_keystorectl.h)(0x00000000)
I (source\ti\driverlib\dl_lcd.hhource\ti\driverlib\dl_mathacl.h)(0x00000000)
I (source\ti\driverlib\dl_mcan.hoource\ti\driverlib\dl_opa.h)(0x00000000)
I (source\ti\driverlib\dl_rtc.hhource\ti\driverlib\dl_rtc_common.h)(0x00000000)
I (source\ti\driverlib\dl_rtc_a.huource\ti\driverlib\dl_scratchpad.h)(0x00000000)
I (source\ti\driverlib\dl_spi.h.ource\ti\driverlib\dl_tamperio.h)(0x00000000)
I (source\ti\driverlib\dl_timera.hrource\ti\driverlib\dl_timer.h)(0x00000000)
I (source\ti\driverlib\dl_timerg.hrource\ti\driverlib\dl_trng.h)(0x00000000)
I (source\ti\driverlib\dl_uart_extend.heource\ti\driverlib\dl_uart.h)(0x00000000)
I (source\ti\driverlib\dl_uart_main.h.ource\ti\driverlib\dl_vref.h)(0x00000000)
I (source\ti\driverlib\dl_wwdt.haource\ti\driverlib\m0p\dl_interrupt.h)(0x00000000)
I (source\ti\driverlib\m0p\dl_systick.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x6569B012)
F (.\User\software_iic.c)(0x687F597B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O2 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./source/third_party/CMSIS/Core/Include -I ./source -I ./BSP -I ./User -I ../../Software_IIC

-D__UVISION_VERSION="539" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/software_iic.o -MD)
I (..\..\Software_IIC\ti_msp_dl_config.hhource\ti\devices\msp\msp.h)(0x00000000)
I (source\ti\devices\DeviceFamily.h)(0x65B1D8C1)
I (source\ti\devices\msp\m0p\mspm0g350x.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x6569B012)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x6569B012)
I (source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_version.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_compiler.h)(0x65B1D8C1)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\cmsis_armclang.h)(0x65B1D8C1)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_compat.h)(0x64F9642C)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\arm_acle.h)(0x6569B010)
I (C:\Users\<USER>\Desktop\has_mcu\mspm0\examples\Software_IIC\keil\source\third_party\CMSIS\Core\Include\mpu_armv7.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_adc12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_aes.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_comp.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_crc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dac12.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_dma.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_flashctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gpio.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_gptimer.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_i2c.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_iomux.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mathacl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_mcan.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_oa.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_rtc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_spi.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_trng.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_uart.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_vref.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wuc.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\hw_wwdt.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x65B1D8C1)
I (source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\driverlib.hlource\ti\driverlib\dl_adc12.h)(0x00000000)
I (source\ti\driverlib\dl_common.hlource\ti\driverlib\dl_aes.h)(0x00000000)
I (D:\Keil\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x6569B012)
I (source\ti\driverlib\dl_aesadv.hcource\ti\driverlib\dl_comp.h)(0x00000000)
I (source\ti\driverlib\dl_crc.hvource\ti\driverlib\dl_crcp.h)(0x00000000)
I (source\ti\driverlib\dl_dac12.huource\ti\driverlib\dl_dma.h)(0x00000000)
I (source\ti\driverlib\dl_flashctl.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_factoryregion.h)(0x65B1D8C1)
I (source\ti\driverlib\m0p\dl_core.hyource\ti\driverlib\m0p\dl_sysctl.h)(0x00000000)
I (source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x65B1D8C1)
I (source\ti\driverlib\dl_gpamp.h\ource\ti\driverlib\dl_gpio.h)(0x00000000)
I (source\ti\driverlib\dl_i2c.h.ource\ti\driverlib\dl_iwdt.h)(0x00000000)
I (source\ti\driverlib\dl_lfss.hoource\ti\driverlib\dl_keystorectl.h)(0x00000000)
I (source\ti\driverlib\dl_lcd.hhource\ti\driverlib\dl_mathacl.h)(0x00000000)
I (source\ti\driverlib\dl_mcan.hoource\ti\driverlib\dl_opa.h)(0x00000000)
I (source\ti\driverlib\dl_rtc.hhource\ti\driverlib\dl_rtc_common.h)(0x00000000)
I (source\ti\driverlib\dl_rtc_a.huource\ti\driverlib\dl_scratchpad.h)(0x00000000)
I (source\ti\driverlib\dl_spi.h.ource\ti\driverlib\dl_tamperio.h)(0x00000000)
I (source\ti\driverlib\dl_timera.hrource\ti\driverlib\dl_timer.h)(0x00000000)
I (source\ti\driverlib\dl_timerg.hrource\ti\driverlib\dl_trng.h)(0x00000000)
I (source\ti\driverlib\dl_uart_extend.heource\ti\driverlib\dl_uart.h)(0x00000000)
I (source\ti\driverlib\dl_uart_main.h.ource\ti\driverlib\dl_vref.h)(0x00000000)
I (source\ti\driverlib\dl_wwdt.haource\ti\driverlib\m0p\dl_interrupt.h)(0x00000000)
I (source\ti\driverlib\m0p\dl_systick.htser\gw_color_sensor.htSP\Time.h)(0x00000000)
F (../../../source/ti/driverlib/lib/keil/m0p/mspm0g1x0x_g3x0x/driverlib.a)(0x65B1D8C1)()
