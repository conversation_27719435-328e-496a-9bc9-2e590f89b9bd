#ifndef GW_MCU_GRAYSCALE_SENSOR_H_
#define GW_MCU_GRAYSCALE_SENSOR_H_

#include <stdint.h>
#include "gw_grayscale_sensor.h"
#include "software_iic.h"

#ifdef __cplusplus
extern "C" {
#endif

/*************************** 错误代码定义 ***************************/
#define MCU_GRAYSCALE_OK            0x00  // 正常
#define MCU_GRAYSCALE_ERROR_COMM    0x01  // 通信错误
#define MCU_GRAYSCALE_ERROR_INIT    0x02  // 初始化错误
#define MCU_GRAYSCALE_ERROR_CALIB   0x03  // 校准错误
#define MCU_GRAYSCALE_ERROR_TIMEOUT 0x04  // 超时错误
#define MCU_GRAYSCALE_ERROR_INVALID 0xFF  // 无效数据

/*************************** 数据结构定义 ***************************/
typedef struct {
    uint8_t  digital_data;        // 8位数字量
    uint16_t analog_data[8];      // 8通道模拟量
    uint16_t normalized_data[8];  // 8通道归一化数据
    uint8_t  sensor_status;       // 传感器状态
    uint8_t  error_code;          // 错误代码
    uint8_t  firmware_version;    // 固件版本
    uint8_t  is_connected;        // 连接状态
} MCU_Grayscale_Data_t;

typedef struct {
    uint8_t  sensor_address;      // 传感器I2C地址
    uint16_t white_calibration[8]; // 白色校准值
    uint16_t black_calibration[8]; // 黑色校准值
    uint8_t  enabled_channels;    // 启用的通道掩码
    uint8_t  sample_rate;         // 采样率配置
} MCU_Grayscale_Config_t;

/*************************** 初始化接口 ***************************/
/**
 * @brief 传感器初始化
 * @return 0-成功, 其他-错误代码
 */
uint8_t MCU_Grayscale_Init(void);

/**
 * @brief 传感器连接检测
 * @return 0-连接正常, 1-连接失败
 */
uint8_t MCU_Grayscale_Ping(void);

/**
 * @brief 传感器重置
 */
void MCU_Grayscale_Reset(void);

/*************************** 数据获取接口 ***************************/
/**
 * @brief 获取8位数字量（主要循迹接口）
 * @return 8位数字量数据，0xFF表示读取失败
 */
uint8_t MCU_Grayscale_GetDigital(void);

/**
 * @brief 获取8通道模拟量
 * @param analog_values 存储8个通道模拟量的数组
 * @return 0-成功, 其他-错误代码
 */
uint8_t MCU_Grayscale_GetAnalog(uint16_t* analog_values);

/**
 * @brief 获取归一化数据（PID控制用）
 * @param normalized_values 存储8个通道归一化数据的数组
 * @return 0-成功, 其他-错误代码
 */
uint8_t MCU_Grayscale_GetNormalized(uint16_t* normalized_values);

/**
 * @brief 获取单个通道数据
 * @param channel 通道号(1-8)
 * @return 通道模拟量值，0xFFFF表示读取失败
 */
uint16_t MCU_Grayscale_GetChannel(uint8_t channel);

/**
 * @brief 获取完整传感器数据
 * @param data 传感器数据结构指针
 * @return 0-成功, 其他-错误代码
 */
uint8_t MCU_Grayscale_GetAllData(MCU_Grayscale_Data_t* data);

/*************************** 配置接口 ***************************/
/**
 * @brief 设置传感器地址
 * @param new_addr 新的I2C地址
 * @return 0-成功, 其他-错误代码
 */
uint8_t MCU_Grayscale_SetAddress(uint8_t new_addr);

/**
 * @brief 设置校准参数
 * @param white_values 白色校准值数组
 * @param black_values 黑色校准值数组
 * @return 0-成功, 其他-错误代码
 */
uint8_t MCU_Grayscale_SetCalibration(uint16_t* white_values, uint16_t* black_values);

/**
 * @brief 启用/禁用特定通道
 * @param channel_mask 通道掩码(bit0-bit7对应通道1-8)
 * @return 0-成功, 其他-错误代码
 */
uint8_t MCU_Grayscale_EnableChannels(uint8_t channel_mask);

/*************************** 状态查询接口 ***************************/
/**
 * @brief 获取传感器状态
 * @return 传感器状态字节
 */
uint8_t MCU_Grayscale_GetStatus(void);

/**
 * @brief 获取错误信息
 * @return 错误代码
 */
uint8_t MCU_Grayscale_GetError(void);

/**
 * @brief 获取固件版本
 * @return 固件版本号
 */
uint8_t MCU_Grayscale_GetFirmware(void);

/*************************** 便利函数 ***************************/
/**
 * @brief 解析数字量中的单个传感器状态
 * @param digital_data 8位数字量
 * @param sensor_num 传感器编号(1-8)
 * @return 1-检测到线, 0-未检测到线
 */
uint8_t MCU_Grayscale_GetSensorBit(uint8_t digital_data, uint8_t sensor_num);

/**
 * @brief 计算线的加权位置（用于PID控制）
 * @param analog_values 8通道模拟量数组
 * @return 加权位置值(-3500到+3500)
 */
float MCU_Grayscale_CalculatePosition(uint16_t* analog_values);

/**
 * @brief 检测是否丢线
 * @param digital_data 8位数字量
 * @return 1-丢线, 0-正常
 */
uint8_t MCU_Grayscale_IsLineLost(uint8_t digital_data);

#ifdef __cplusplus
}
#endif

#endif /* GW_MCU_GRAYSCALE_SENSOR_H_ */
