# 带MCU版本灰度传感器使用示例

## 🎯 完整主程序示例

### 基本循迹版本
```c
#include "ti_msp_dl_config.h"
#include "gw_mcu_grayscale_sensor.h"

int main(void)
{
    // 1. 系统初始化
    SYSCFG_DL_init();
    
    // 2. 传感器初始化
    if(MCU_Grayscale_Init() != MCU_GRAYSCALE_OK) {
        printf("Sensor initialization failed!\n");
        while(1);  // 停止运行
    }
    
    printf("Grayscale sensor initialized successfully!\n");
    
    // 3. 主循环
    while(1) {
        // 获取数字量数据（一行代码搞定！）
        uint8_t digital_line = MCU_Grayscale_GetDigital();
        
        if(digital_line != MCU_GRAYSCALE_ERROR_INVALID) {
            // 循迹控制逻辑
            line_following_control(digital_line);
        } else {
            // 通信错误处理
            printf("Communication error, retrying...\n");
            MCU_Grayscale_Reset();
            delay_ms(100);
        }
        
        delay_ms(10);  // 100Hz控制频率
    }
}

// 简单的循迹控制函数
void line_following_control(uint8_t digital_data)
{
    if(MCU_Grayscale_IsLineLost(digital_data)) {
        // 丢线处理
        motor_stop();
        printf("Line lost!\n");
    } else if(digital_data == 0x18) {  // 00011000 - 中间传感器
        // 直行
        motor_forward(50, 50);
    } else if(digital_data & 0x0F) {   // 右侧传感器
        // 左转
        motor_forward(30, 70);
    } else if(digital_data & 0xF0) {   // 左侧传感器
        // 右转
        motor_forward(70, 30);
    }
}
```

### 高精度PID控制版本
```c
#include "ti_msp_dl_config.h"
#include "gw_mcu_grayscale_sensor.h"

// PID参数
float kp = 2.0, ki = 0.1, kd = 0.5;
float last_error = 0, integral = 0;

int main(void)
{
    SYSCFG_DL_init();
    
    if(MCU_Grayscale_Init() != MCU_GRAYSCALE_OK) {
        printf("Sensor init failed!\n");
        while(1);
    }
    
    while(1) {
        advanced_line_following_pid();
        delay_ms(10);
    }
}

void advanced_line_following_pid(void)
{
    uint16_t analog_values[8];
    
    // 获取模拟量数据
    if(MCU_Grayscale_GetAnalog(analog_values) == MCU_GRAYSCALE_OK) {
        
        // 计算加权位置
        float line_position = MCU_Grayscale_CalculatePosition(analog_values);
        
        // PID控制
        float error = line_position;
        integral += error;
        float derivative = error - last_error;
        
        float pid_output = kp * error + ki * integral + kd * derivative;
        
        // 限幅
        if(pid_output > 100) pid_output = 100;
        if(pid_output < -100) pid_output = -100;
        
        // 电机控制
        int left_speed = 50 - (int)pid_output;
        int right_speed = 50 + (int)pid_output;
        
        motor_forward(left_speed, right_speed);
        
        last_error = error;
    }
}
```

### 完整数据监控版本
```c
#include "ti_msp_dl_config.h"
#include "gw_mcu_grayscale_sensor.h"

int main(void)
{
    SYSCFG_DL_init();
    
    if(MCU_Grayscale_Init() != MCU_GRAYSCALE_OK) {
        printf("Sensor init failed!\n");
        while(1);
    }
    
    // 打印传感器信息
    printf("Firmware version: 0x%02X\n", MCU_Grayscale_GetFirmware());
    
    while(1) {
        MCU_Grayscale_Data_t sensor_data;
        
        // 获取完整传感器数据
        if(MCU_Grayscale_GetAllData(&sensor_data) == MCU_GRAYSCALE_OK) {
            
            // 打印数字量
            printf("Digital: 0x%02X ", sensor_data.digital_data);
            
            // 打印模拟量
            printf("Analog: ");
            for(int i = 0; i < 8; i++) {
                printf("%4d ", sensor_data.analog_data[i]);
            }
            
            // 打印归一化数据
            printf("Normalized: ");
            for(int i = 0; i < 8; i++) {
                printf("%4d ", sensor_data.normalized_data[i]);
            }
            
            printf("\n");
            
            // 循迹控制
            line_following_control(sensor_data.digital_data);
            
        } else {
            printf("Data read failed!\n");
        }
        
        delay_ms(100);  // 10Hz监控频率
    }
}
```

## 🔧 校准功能示例

### 自动校准程序
```c
void auto_calibration(void)
{
    uint16_t white_values[8] = {0};
    uint16_t black_values[8] = {4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095};
    uint16_t temp_values[8];
    
    printf("Starting auto calibration...\n");
    
    // 白色校准（传感器在白色表面上）
    printf("Place sensor on WHITE surface, press any key...\n");
    getchar();
    
    for(int i = 0; i < 10; i++) {
        if(MCU_Grayscale_GetAnalog(temp_values) == MCU_GRAYSCALE_OK) {
            for(int j = 0; j < 8; j++) {
                if(temp_values[j] > white_values[j]) {
                    white_values[j] = temp_values[j];
                }
            }
        }
        delay_ms(100);
    }
    
    // 黑色校准（传感器在黑线上）
    printf("Place sensor on BLACK line, press any key...\n");
    getchar();
    
    for(int i = 0; i < 10; i++) {
        if(MCU_Grayscale_GetAnalog(temp_values) == MCU_GRAYSCALE_OK) {
            for(int j = 0; j < 8; j++) {
                if(temp_values[j] < black_values[j]) {
                    black_values[j] = temp_values[j];
                }
            }
        }
        delay_ms(100);
    }
    
    // 设置校准参数
    if(MCU_Grayscale_SetCalibration(white_values, black_values) == MCU_GRAYSCALE_OK) {
        printf("Calibration completed successfully!\n");
        
        printf("White values: ");
        for(int i = 0; i < 8; i++) {
            printf("%4d ", white_values[i]);
        }
        printf("\n");
        
        printf("Black values: ");
        for(int i = 0; i < 8; i++) {
            printf("%4d ", black_values[i]);
        }
        printf("\n");
    } else {
        printf("Calibration failed!\n");
    }
}
```

## 🎮 API对比表

| 功能 | 无MCU版本 | 带MCU版本 | 优势 |
|------|-----------|-----------|------|
| **初始化** | `No_MCU_Ganv_Sensor_Init(&sensor, white, black)` | `MCU_Grayscale_Init()` | 更简单，无需结构体 |
| **获取数字量** | `Get_Digtal_For_User(&sensor)` | `MCU_Grayscale_GetDigital()` | 直接调用，无需结构体 |
| **获取模拟量** | `Get_Normalize_For_User(&sensor, result)` | `MCU_Grayscale_GetAnalog(result)` | 接口更清晰 |
| **任务处理** | `No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor)` | 无需调用 | 传感器内部处理 |
| **错误处理** | 返回值检查 | 统一错误代码 | 更规范的错误处理 |

## 💡 使用建议

### 1. 基本循迹应用
```c
// 最简单的使用方式
uint8_t digital_data = MCU_Grayscale_GetDigital();
if(digital_data != MCU_GRAYSCALE_ERROR_INVALID) {
    // 使用digital_data进行循迹控制
}
```

### 2. 高精度应用
```c
// 使用模拟量进行PID控制
uint16_t analog_values[8];
if(MCU_Grayscale_GetAnalog(analog_values) == MCU_GRAYSCALE_OK) {
    float position = MCU_Grayscale_CalculatePosition(analog_values);
    // 使用position进行PID控制
}
```

### 3. 调试应用
```c
// 获取完整数据进行调试
MCU_Grayscale_Data_t data;
if(MCU_Grayscale_GetAllData(&data) == MCU_GRAYSCALE_OK) {
    // 打印所有传感器数据
}
```
