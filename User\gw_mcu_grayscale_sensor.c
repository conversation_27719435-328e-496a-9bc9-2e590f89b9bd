#include "gw_mcu_grayscale_sensor.h"

/*************************** 私有变量 ***************************/
static MCU_Grayscale_Config_t sensor_config = {
    .sensor_address = GW_GRAY_ADDR_DEF,
    .enabled_channels = 0xFF,  // 默认启用所有通道
    .sample_rate = 100         // 默认100Hz采样率
};

/*************************** 初始化接口实现 ***************************/
uint8_t MCU_Grayscale_Init(void)
{
    // 检测传感器连接
    if(MCU_Grayscale_Ping() != 0) {
        return MCU_GRAYSCALE_ERROR_INIT;
    }
    
    // 启用所有通道
    if(MCU_Grayscale_EnableChannels(0xFF) != 0) {
        return MCU_GRAYSCALE_ERROR_INIT;
    }
    
    return MCU_GRAYSCALE_OK;
}

uint8_t MCU_Grayscale_Ping(void)
{
    return Ping();  // 使用现有的Ping函数
}

void MCU_Grayscale_Reset(void)
{
    i2c_reset();  // 使用现有的重置函数
}

/*************************** 数据获取接口实现 ***************************/
uint8_t MCU_Grayscale_GetDigital(void)
{
    uint8_t digital_data = 0;
    
    if(IIC_ReadBytes(sensor_config.sensor_address, GW_GRAY_DIGITAL_MODE, 
                    &digital_data, 1) == 1) {
        return digital_data;
    }
    
    return MCU_GRAYSCALE_ERROR_INVALID;  // 读取失败
}

uint8_t MCU_Grayscale_GetAnalog(uint16_t* analog_values)
{
    uint8_t buffer[16];  // 8个通道 × 2字节
    
    if(analog_values == NULL) {
        return MCU_GRAYSCALE_ERROR_INVALID;
    }
    
    if(IIC_ReadBytes(sensor_config.sensor_address, GW_GRAY_ANALOG_MODE, 
                    buffer, 16) == 1) {
        // 将字节数据转换为16位数值（大端序）
        for(int i = 0; i < 8; i++) {
            analog_values[i] = (buffer[i*2] << 8) | buffer[i*2+1];
        }
        return MCU_GRAYSCALE_OK;
    }
    
    return MCU_GRAYSCALE_ERROR_COMM;
}

uint8_t MCU_Grayscale_GetNormalized(uint16_t* normalized_values)
{
    uint8_t buffer[16];  // 8个通道 × 2字节
    
    if(normalized_values == NULL) {
        return MCU_GRAYSCALE_ERROR_INVALID;
    }
    
    if(IIC_ReadBytes(sensor_config.sensor_address, GW_GRAY_ANALOG_NORMALIZE, 
                    buffer, 16) == 1) {
        // 将字节数据转换为16位数值
        for(int i = 0; i < 8; i++) {
            normalized_values[i] = (buffer[i*2] << 8) | buffer[i*2+1];
        }
        return MCU_GRAYSCALE_OK;
    }
    
    return MCU_GRAYSCALE_ERROR_COMM;
}

uint16_t MCU_Grayscale_GetChannel(uint8_t channel)
{
    uint8_t buffer[2];
    
    if(channel < 1 || channel > 8) {
        return 0xFFFF;  // 无效通道
    }
    
    if(IIC_ReadBytes(sensor_config.sensor_address, GW_GRAY_ANALOG(channel), 
                    buffer, 2) == 1) {
        return (buffer[0] << 8) | buffer[1];
    }
    
    return 0xFFFF;  // 读取失败
}

uint8_t MCU_Grayscale_GetAllData(MCU_Grayscale_Data_t* data)
{
    if(data == NULL) {
        return MCU_GRAYSCALE_ERROR_INVALID;
    }
    
    // 获取数字量
    data->digital_data = MCU_Grayscale_GetDigital();
    if(data->digital_data == MCU_GRAYSCALE_ERROR_INVALID) {
        data->is_connected = 0;
        return MCU_GRAYSCALE_ERROR_COMM;
    }
    
    // 获取模拟量
    if(MCU_Grayscale_GetAnalog(data->analog_data) != MCU_GRAYSCALE_OK) {
        data->is_connected = 0;
        return MCU_GRAYSCALE_ERROR_COMM;
    }
    
    // 获取归一化数据
    MCU_Grayscale_GetNormalized(data->normalized_data);
    
    // 获取状态信息
    data->sensor_status = MCU_Grayscale_GetStatus();
    data->error_code = MCU_Grayscale_GetError();
    data->firmware_version = MCU_Grayscale_GetFirmware();
    data->is_connected = 1;
    
    return MCU_GRAYSCALE_OK;
}

/*************************** 配置接口实现 ***************************/
uint8_t MCU_Grayscale_SetAddress(uint8_t new_addr)
{
    if(IIC_WriteByte(sensor_config.sensor_address, GW_GRAY_CHANGE_ADDR, 
                    new_addr) == 1) {
        sensor_config.sensor_address = new_addr;
        return MCU_GRAYSCALE_OK;
    }
    
    return MCU_GRAYSCALE_ERROR_COMM;
}

uint8_t MCU_Grayscale_SetCalibration(uint16_t* white_values, uint16_t* black_values)
{
    uint8_t buffer[16];
    
    if(white_values == NULL || black_values == NULL) {
        return MCU_GRAYSCALE_ERROR_INVALID;
    }
    
    // 设置白色校准值
    for(int i = 0; i < 8; i++) {
        buffer[i*2] = (white_values[i] >> 8) & 0xFF;
        buffer[i*2+1] = white_values[i] & 0xFF;
    }
    
    if(IIC_WriteBytes(sensor_config.sensor_address, GW_GRAY_CALIBRATION_WHITE, 
                     buffer, 16) != 1) {
        return MCU_GRAYSCALE_ERROR_COMM;
    }
    
    // 设置黑色校准值
    for(int i = 0; i < 8; i++) {
        buffer[i*2] = (black_values[i] >> 8) & 0xFF;
        buffer[i*2+1] = black_values[i] & 0xFF;
    }
    
    if(IIC_WriteBytes(sensor_config.sensor_address, GW_GRAY_CALIBRATION_BLACK, 
                     buffer, 16) != 1) {
        return MCU_GRAYSCALE_ERROR_COMM;
    }
    
    return MCU_GRAYSCALE_OK;
}

uint8_t MCU_Grayscale_EnableChannels(uint8_t channel_mask)
{
    if(IIC_WriteByte(sensor_config.sensor_address, GW_GRAY_ANALOG_CHANNEL_ENABLE, 
                    channel_mask) == 1) {
        sensor_config.enabled_channels = channel_mask;
        return MCU_GRAYSCALE_OK;
    }
    
    return MCU_GRAYSCALE_ERROR_COMM;
}

/*************************** 状态查询接口实现 ***************************/
uint8_t MCU_Grayscale_GetStatus(void)
{
    uint8_t status = 0;
    IIC_ReadBytes(sensor_config.sensor_address, GW_GRAY_PING, &status, 1);
    return status;
}

uint8_t MCU_Grayscale_GetError(void)
{
    uint8_t error = 0;
    IIC_ReadBytes(sensor_config.sensor_address, GW_GRAY_ERROR, &error, 1);
    return error;
}

uint8_t MCU_Grayscale_GetFirmware(void)
{
    uint8_t firmware = 0;
    IIC_ReadBytes(sensor_config.sensor_address, GW_GRAY_FIRMWARE, &firmware, 1);
    return firmware;
}

/*************************** 便利函数实现 ***************************/
uint8_t MCU_Grayscale_GetSensorBit(uint8_t digital_data, uint8_t sensor_num)
{
    if(sensor_num < 1 || sensor_num > 8) {
        return 0;
    }
    
    return (digital_data >> (sensor_num - 1)) & 0x01;
}

float MCU_Grayscale_CalculatePosition(uint16_t* analog_values)
{
    float weighted_sum = 0;
    float total_weight = 0;
    
    if(analog_values == NULL) {
        return 0;
    }
    
    for(int i = 0; i < 8; i++) {
        float weight = analog_values[i] / 4095.0;  // 归一化到0-1
        weighted_sum += weight * (i - 3.5) * 1000;
        total_weight += weight;
    }
    
    if(total_weight > 0.1) {
        return weighted_sum / total_weight;
    }
    
    return 0;  // 丢线情况
}

uint8_t MCU_Grayscale_IsLineLost(uint8_t digital_data)
{
    // 如果所有传感器都没有检测到线，认为丢线
    return (digital_data == 0x00) ? 1 : 0;
}
