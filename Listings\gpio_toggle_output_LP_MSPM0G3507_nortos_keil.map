Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    gpio_toggle_output.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    gpio_toggle_output.o(.text.main) refers to software_iic.o(.text.i2c_reset) for i2c_reset
    gpio_toggle_output.o(.text.main) refers to uart.o(.text.uart0_send_string) for uart0_send_string
    gpio_toggle_output.o(.text.main) refers to rt_memclr.o(.text) for __aeabi_memclr4
    gpio_toggle_output.o(.text.main) refers to software_iic.o(.text.Ping) for Ping
    gpio_toggle_output.o(.text.main) refers to time.o(.text.delay_ms) for delay_ms
    gpio_toggle_output.o(.text.main) refers to software_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    gpio_toggle_output.o(.text.main) refers to software_iic.o(.text.IIC_WriteBytes) for IIC_WriteBytes
    gpio_toggle_output.o(.text.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    gpio_toggle_output.o(.text.main) refers to gpio_toggle_output.o(.bss.rx_buff) for rx_buff
    gpio_toggle_output.o(.text.main) refers to gpio_toggle_output.o(.bss.Digtal) for Digtal
    gpio_toggle_output.o(.text.main) refers to gpio_toggle_output.o(.bss.Anolog) for Anolog
    gpio_toggle_output.o(.text.main) refers to gpio_toggle_output.o(.bss.IIC_write_buff) for IIC_write_buff
    gpio_toggle_output.o(.text.main) refers to gpio_toggle_output.o(.bss.Normal) for Normal
    gpio_toggle_output.o(.text.main) refers to gpio_toggle_output.o(.rodata.str1.1) for [Anonymous Symbol]
    gpio_toggle_output.o(.ARM.exidx.text.main) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.ARM.exidx.text.main) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.ARM.exidx.text.main) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.ARM.exidx.text.main) refers to gpio_toggle_output.o(.text.main) for [Anonymous Symbol]
    gpio_toggle_output.o(.bss.rx_buff) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.bss.rx_buff) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.bss.rx_buff) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.bss.IIC_write_buff) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.bss.IIC_write_buff) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.bss.IIC_write_buff) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.bss.Anolog) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.bss.Anolog) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.bss.Anolog) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.bss.Normal) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.bss.Normal) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.bss.Normal) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.bss.Digtal) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.bss.Digtal) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.bss.Digtal) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    gpio_toggle_output.o(.ARM.use_no_argv) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    gpio_toggle_output.o(.ARM.use_no_argv) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    gpio_toggle_output.o(.ARM.use_no_argv) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    startup_mspm0g350x_uvision.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g350x_uvision.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(HEAP) for Heap_Mem
    startup_mspm0g350x_uvision.o(.text) refers to startup_mspm0g350x_uvision.o(STACK) for Stack_Mem
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for SYSCFG_DL_I2C_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init) for SYSCFG_DL_ADC1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for SYSCFG_DL_DMA_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for DL_ADC12_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init) refers to ti_msp_dl_config.o(.rodata.gADC1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for SYSCFG_DL_DMA_CH0_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to dl_dma.o(.text.DL_DMA_initChannel) for DL_DMA_initChannel
    ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.rodata.gDMA_CH0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_us) refers to time.o(.text.delay_us) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_ms) refers to time.o(.text.delay_ms) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.uart0_send_char) refers to uart.o(.text.uart0_send_char) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.uart0_send_string) refers to uart.o(.text.uart0_send_string) for [Anonymous Symbol]
    software_iic.o(.text.IIC_Start) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.ARM.exidx.text.IIC_Start) refers to software_iic.o(.text.IIC_Start) for [Anonymous Symbol]
    software_iic.o(.text.IIC_Stop) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.ARM.exidx.text.IIC_Stop) refers to software_iic.o(.text.IIC_Stop) for [Anonymous Symbol]
    software_iic.o(.text.IIC_WaitAck) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.ARM.exidx.text.IIC_WaitAck) refers to software_iic.o(.text.IIC_WaitAck) for [Anonymous Symbol]
    software_iic.o(.text.IIC_SendAck) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.ARM.exidx.text.IIC_SendAck) refers to software_iic.o(.text.IIC_SendAck) for [Anonymous Symbol]
    software_iic.o(.text.IIC_SendNAck) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.ARM.exidx.text.IIC_SendNAck) refers to software_iic.o(.text.IIC_SendNAck) for [Anonymous Symbol]
    software_iic.o(.text.IIC_SendByte) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.ARM.exidx.text.IIC_SendByte) refers to software_iic.o(.text.IIC_SendByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_RecvByte) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.ARM.exidx.text.IIC_RecvByte) refers to software_iic.o(.text.IIC_RecvByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_ReadByte) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.text.IIC_ReadByte) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.text.IIC_ReadByte) refers to software_iic.o(.text.IIC_RecvByte) for IIC_RecvByte
    software_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to software_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_ReadBytes) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.text.IIC_ReadBytes) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.text.IIC_ReadBytes) refers to software_iic.o(.text.IIC_RecvByte) for IIC_RecvByte
    software_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to software_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    software_iic.o(.text.IIC_WriteByte) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.text.IIC_WriteByte) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to software_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    software_iic.o(.text.IIC_WriteBytes) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.text.IIC_WriteBytes) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to software_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    software_iic.o(.text.Ping) refers to software_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(.ARM.exidx.text.Ping) refers to software_iic.o(.text.Ping) for [Anonymous Symbol]
    software_iic.o(.text.i2c_begin_transmission) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.text.i2c_begin_transmission) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.i2c_begin_transmission) refers to software_iic.o(.text.i2c_begin_transmission) for [Anonymous Symbol]
    software_iic.o(.text.i2c_write) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.ARM.exidx.text.i2c_write) refers to software_iic.o(.text.i2c_write) for [Anonymous Symbol]
    software_iic.o(.text.i2c_end_transmission) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.ARM.exidx.text.i2c_end_transmission) refers to software_iic.o(.text.i2c_end_transmission) for [Anonymous Symbol]
    software_iic.o(.text.i2c_reset) refers to time.o(.text.delay_us) for delay_us
    software_iic.o(.text.i2c_reset) refers to software_iic.o(.text.IIC_SendByte) for IIC_SendByte
    software_iic.o(.text.i2c_reset) refers to software_iic.o(.data.reset_magic_number) for reset_magic_number
    software_iic.o(.ARM.exidx.text.i2c_reset) refers to software_iic.o(.text.i2c_reset) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to aeabi_sdivfast.o(.text_divfast) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to rtudiv10.o(.text) for __rt_udiv10
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to gpio_toggle_output.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g350x_uvision.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing gpio_toggle_output.o(.text), (0 bytes).
    Removing gpio_toggle_output.o(.ARM.exidx.text.main), (8 bytes).
    Removing gpio_toggle_output.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_I2C_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_ADC1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_DMA_CH0_init), (8 bytes).
    Removing time.o(.text), (0 bytes).
    Removing time.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing time.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing uart.o(.text), (0 bytes).
    Removing uart.o(.text.uart0_send_char), (36 bytes).
    Removing uart.o(.ARM.exidx.text.uart0_send_char), (8 bytes).
    Removing uart.o(.ARM.exidx.text.uart0_send_string), (8 bytes).
    Removing software_iic.o(.text), (0 bytes).
    Removing software_iic.o(.text.IIC_Start), (40 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing software_iic.o(.text.IIC_Stop), (40 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing software_iic.o(.text.IIC_WaitAck), (52 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WaitAck), (8 bytes).
    Removing software_iic.o(.text.IIC_SendAck), (28 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendAck), (8 bytes).
    Removing software_iic.o(.text.IIC_SendNAck), (28 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendNAck), (8 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_SendByte), (8 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_RecvByte), (8 bytes).
    Removing software_iic.o(.text.IIC_ReadByte), (100 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing software_iic.o(.text.IIC_WriteByte), (148 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing software_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing software_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing software_iic.o(.text.i2c_begin_transmission), (84 bytes).
    Removing software_iic.o(.ARM.exidx.text.i2c_begin_transmission), (8 bytes).
    Removing software_iic.o(.text.i2c_write), (48 bytes).
    Removing software_iic.o(.ARM.exidx.text.i2c_write), (8 bytes).
    Removing software_iic.o(.text.i2c_end_transmission), (40 bytes).
    Removing software_iic.o(.ARM.exidx.text.i2c_end_transmission), (8 bytes).
    Removing software_iic.o(.ARM.exidx.text.i2c_reset), (8 bytes).
    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).

134 unused section(s) (total 2992 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.c                       0x00000000   Number         0  rtudiv10.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    Time.c                                   0x00000000   Number         0  time.o ABSOLUTE
    Uart.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    gpio_toggle_output.c                     0x00000000   Number         0  gpio_toggle_output.o ABSOLUTE
    software_iic.c                           0x00000000   Number         0  software_iic.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    !!!main                                  0x000000c0   Section        8  __main.o(!!!main)
    !!!scatter                               0x000000c8   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000120   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000140   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000148   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x00000164   Section        2  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x00000166   Section       10  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x00000170   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x00000174   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x00000176   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x00000176   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000178   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0000017a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0000017a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0000017c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0000017c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0000017c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x00000182   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000182   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x00000186   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x00000186   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0000018e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x00000190   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x00000190   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x00000194   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0000019c   Section       48  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000001cc   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x000001f4   Section        0  __printf.o(.text)
    .text                                    0x00000260   Section        0  _printf_dec.o(.text)
    .text                                    0x000002cc   Section        0  rt_memclr.o(.text)
    .text                                    0x0000030c   Section        0  heapauxi.o(.text)
    .text                                    0x00000312   Section        0  _printf_intcommon.o(.text)
    _printf_input_char                       0x000003c5   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x000003c4   Section        0  _printf_char_common.o(.text)
    .text                                    0x000003f4   Section        0  _sputc.o(.text)
    .text                                    0x000003fe   Section        0  rtudiv10.o(.text)
    .text                                    0x00000426   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x00000464   Section        0  exit.o(.text)
    .text                                    0x00000474   Section        8  libspace.o(.text)
    .text                                    0x0000047c   Section        0  sys_exit.o(.text)
    .text                                    0x00000488   Section        2  use_no_semi.o(.text)
    .text                                    0x0000048a   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x0000048c   Section        0  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_0                             0x000004c4   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    __arm_cp.0_1                             0x000004c8   Number         4  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    [Anonymous Symbol]                       0x000004cc   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x000004d8   Section        0  dl_dma.o(.text.DL_DMA_initChannel)
    __arm_cp.0_0                             0x00000518   Number         4  dl_dma.o(.text.DL_DMA_initChannel)
    [Anonymous Symbol]                       0x0000051c   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    [Anonymous Symbol]                       0x00000544   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_0                             0x000005f8   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x000005fc   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00000600   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00000604   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    [Anonymous Symbol]                       0x00000608   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x00000654   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    [Anonymous Symbol]                       0x00000658   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00000678   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x0000067c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    [Anonymous Symbol]                       0x00000680   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x000006c0   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x000006c4   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x000006c8   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x000006dc   Section        0  software_iic.o(.text.IIC_ReadBytes)
    [Anonymous Symbol]                       0x000007c4   Section        0  software_iic.o(.text.IIC_RecvByte)
    __arm_cp.6_1                             0x000008d0   Number         4  software_iic.o(.text.IIC_RecvByte)
    __arm_cp.6_2                             0x000008d4   Number         4  software_iic.o(.text.IIC_RecvByte)
    [Anonymous Symbol]                       0x000008d8   Section        0  software_iic.o(.text.IIC_SendByte)
    __arm_cp.5_1                             0x00000a08   Number         4  software_iic.o(.text.IIC_SendByte)
    [Anonymous Symbol]                       0x00000a0c   Section        0  software_iic.o(.text.IIC_WriteBytes)
    __arm_cp.10_0                            0x00000a9c   Number         4  software_iic.o(.text.IIC_WriteBytes)
    [Anonymous Symbol]                       0x00000aa0   Section        0  software_iic.o(.text.Ping)
    [Anonymous Symbol]                       0x00000ac0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_0                             0x00000afc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_1                             0x00000b00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_2                             0x00000b04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_3                             0x00000b08   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    __arm_cp.7_4                             0x00000b0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    [Anonymous Symbol]                       0x00000b10   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.10_0                            0x00000b20   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    __arm_cp.10_1                            0x00000b24   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    [Anonymous Symbol]                       0x00000b28   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    [Anonymous Symbol]                       0x00000b30   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x00000b88   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x00000b8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x00000b90   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x00000b94   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x00000b98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x00000b9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x00000ba0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00000ba4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.4_0                             0x00000bec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.4_1                             0x00000bf0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    __arm_cp.4_2                             0x00000bf4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    [Anonymous Symbol]                       0x00000bf8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00000c7c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x00000c80   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_2                             0x00000c84   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00000c88   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.9_0                             0x00000ca0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00000ca4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_0                             0x00000cf0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_1                             0x00000cf4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_2                             0x00000cf8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.5_3                             0x00000cfc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00000d00   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_0                             0x00000d5c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_1                             0x00000d60   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_2                             0x00000d64   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_3                             0x00000d68   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_4                             0x00000d6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.6_5                             0x00000d70   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x00000d74   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00000d9c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00000dd0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00000dd4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00000dd8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00000ddc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00000de0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00000de4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00000de8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00000dec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00000df0   Section        0  time.o(.text.delay_ms)
    __arm_cp.1_0                             0x00000e30   Number         4  time.o(.text.delay_ms)
    [Anonymous Symbol]                       0x00000e34   Section        0  time.o(.text.delay_us)
    __arm_cp.0_0                             0x00000e74   Number         4  time.o(.text.delay_us)
    [Anonymous Symbol]                       0x00000e78   Section        0  software_iic.o(.text.i2c_reset)
    __arm_cp.15_0                            0x00000f28   Number         4  software_iic.o(.text.i2c_reset)
    __arm_cp.15_1                            0x00000f2c   Number         4  software_iic.o(.text.i2c_reset)
    [Anonymous Symbol]                       0x00000f30   Section        0  gpio_toggle_output.o(.text.main)
    __arm_cp.0_1                             0x000010dc   Number         4  gpio_toggle_output.o(.text.main)
    __arm_cp.0_4                             0x0000110c   Number         4  gpio_toggle_output.o(.text.main)
    __arm_cp.0_5                             0x00001110   Number         4  gpio_toggle_output.o(.text.main)
    __arm_cp.0_6                             0x00001114   Number         4  gpio_toggle_output.o(.text.main)
    __arm_cp.0_7                             0x00001118   Number         4  gpio_toggle_output.o(.text.main)
    __arm_cp.0_8                             0x0000111c   Number         4  gpio_toggle_output.o(.text.main)
    [Anonymous Symbol]                       0x00001168   Section        0  uart.o(.text.uart0_send_string)
    __arm_cp.1_0                             0x0000119c   Number         4  uart.o(.text.uart0_send_string)
    gADC1ClockConfig                         0x000011a0   Data           8  ti_msp_dl_config.o(.rodata.gADC1ClockConfig)
    [Anonymous Symbol]                       0x000011a0   Section        0  ti_msp_dl_config.o(.rodata.gADC1ClockConfig)
    gDMA_CH0Config                           0x000011a8   Data          24  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    [Anonymous Symbol]                       0x000011a8   Section        0  ti_msp_dl_config.o(.rodata.gDMA_CH0Config)
    gI2C_0ClockConfig                        0x000011c0   Data           2  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    [Anonymous Symbol]                       0x000011c0   Section        0  ti_msp_dl_config.o(.rodata.gI2C_0ClockConfig)
    gSYSPLLConfig                            0x000011c4   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x000011c4   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gUART_0ClockConfig                       0x000011ec   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x000011ec   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x000011ee   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x000011ee   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_1ClockConfig                       0x000011f8   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x000011f8   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x000011fa   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x000011fa   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x00001204   Section        0  gpio_toggle_output.o(.rodata.str1.1)
    .bss                                     0x20200008   Section       96  libspace.o(.bss)
    Heap_Mem                                 0x20200188   Data        4096  startup_mspm0g350x_uvision.o(HEAP)
    HEAP                                     0x20200188   Section     4096  startup_mspm0g350x_uvision.o(HEAP)
    Stack_Mem                                0x20201188   Data        4096  startup_mspm0g350x_uvision.o(STACK)
    STACK                                    0x20201188   Section     4096  startup_mspm0g350x_uvision.o(STACK)
    __initial_sp                             0x20202188   Data           0  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x000000c9   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x000000c9   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x000000d3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x00000121   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x00000141   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x00000149   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x00000165   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x00000167   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent_end                      0x00000171   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x00000175   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x00000177   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x00000179   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0000017b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0000017d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0000017d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0000017d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x00000183   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x00000183   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x00000187   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x00000187   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0000018f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x00000191   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x00000191   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x00000195   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0000019d   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000001a1   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000001a3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000001a5   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000001a7   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SysTick_Handler                          0x000001a9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000001ab   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART0_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART3_IRQHandler                         0x000001ab   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __user_initial_stackheap                 0x000001ad   Thumb Code    10  startup_mspm0g350x_uvision.o(.text)
    __2sprintf                               0x000001cd   Thumb Code    36  noretval__2sprintf.o(.text)
    __printf                                 0x000001f5   Thumb Code   108  __printf.o(.text)
    _printf_int_dec                          0x00000261   Thumb Code    90  _printf_dec.o(.text)
    _memset_w                                0x000002cd   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x000002e7   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x00000305   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x00000305   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x00000309   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x00000309   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x00000309   Thumb Code     4  rt_memclr.o(.text)
    __use_two_region_memory                  0x0000030d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0000030f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x00000311   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x00000313   Thumb Code   176  _printf_intcommon.o(.text)
    _printf_char_common                      0x000003cf   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x000003f5   Thumb Code    10  _sputc.o(.text)
    __rt_udiv10                              0x000003ff   Thumb Code    40  rtudiv10.o(.text)
    __user_setup_stackheap                   0x00000427   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x00000465   Thumb Code    16  exit.o(.text)
    __user_libspace                          0x00000475   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x00000475   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x00000475   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x0000047d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x00000489   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x00000489   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0000048b   Thumb Code     0  indicate_semi.o(.text)
    DL_ADC12_setClockConfig                  0x0000048d   Thumb Code    64  dl_adc12.o(.text.DL_ADC12_setClockConfig)
    DL_Common_delayCycles                    0x000004cd   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_DMA_initChannel                       0x000004d9   Thumb Code    68  dl_dma.o(.text.DL_DMA_initChannel)
    DL_I2C_setClockConfig                    0x0000051d   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SYSCTL_configSYSPLL                   0x00000545   Thumb Code   196  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x00000609   Thumb Code    80  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00000659   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_UART_init                             0x00000681   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000006c9   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    IIC_ReadBytes                            0x000006dd   Thumb Code   232  software_iic.o(.text.IIC_ReadBytes)
    IIC_RecvByte                             0x000007c5   Thumb Code   268  software_iic.o(.text.IIC_RecvByte)
    IIC_SendByte                             0x000008d9   Thumb Code   304  software_iic.o(.text.IIC_SendByte)
    IIC_WriteBytes                           0x00000a0d   Thumb Code   144  software_iic.o(.text.IIC_WriteBytes)
    Ping                                     0x00000aa1   Thumb Code    30  software_iic.o(.text.Ping)
    SYSCFG_DL_ADC1_init                      0x00000ac1   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_ADC1_init)
    SYSCFG_DL_DMA_CH0_init                   0x00000b11   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init)
    SYSCFG_DL_DMA_init                       0x00000b29   Thumb Code     8  ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init)
    SYSCFG_DL_GPIO_init                      0x00000b31   Thumb Code    88  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_I2C_0_init                     0x00000ba5   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init)
    SYSCFG_DL_SYSCTL_init                    0x00000bf9   Thumb Code   132  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00000c89   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_UART_0_init                    0x00000ca5   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_1_init                    0x00000d01   Thumb Code    92  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_init                           0x00000d75   Thumb Code    40  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00000d9d   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    delay_ms                                 0x00000df1   Thumb Code    64  time.o(.text.delay_ms)
    delay_us                                 0x00000e35   Thumb Code    64  time.o(.text.delay_us)
    i2c_reset                                0x00000e79   Thumb Code   176  software_iic.o(.text.i2c_reset)
    main                                     0x00000f31   Thumb Code   412  gpio_toggle_output.o(.text.main)
    uart0_send_string                        0x00001169   Thumb Code    52  uart.o(.text.uart0_send_string)
    Region$$Table$$Base                      0x00001228   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00001248   Number         0  anon$$obj.o(Region$$Table)
    reset_magic_number                       0x20200000   Data           8  software_iic.o(.data.reset_magic_number)
    __libspace_start                         0x20200008   Data          96  libspace.o(.bss)
    Anolog                                   0x20200068   Data           8  gpio_toggle_output.o(.bss.Anolog)
    __temporary_stack_top$libspace           0x20200068   Data           0  libspace.o(.bss)
    Digtal                                   0x20200070   Data           1  gpio_toggle_output.o(.bss.Digtal)
    IIC_write_buff                           0x20200071   Data          10  gpio_toggle_output.o(.bss.IIC_write_buff)
    Normal                                   0x2020007b   Data           8  gpio_toggle_output.o(.bss.Normal)
    rx_buff                                  0x20200084   Data         256  gpio_toggle_output.o(.bss.rx_buff)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00001250, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00001248, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO           18    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000008   Code   RO          307  * !!!main             c_p.l(__main.o)
    0x000000c8   0x000000c8   0x00000054   Code   RO          495    !!!scatter          c_p.l(__scatter.o)
    0x0000011c   0x0000011c   0x00000004   PAD
    0x00000120   0x00000120   0x0000001a   Code   RO          499    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0000013a   0x0000013a   0x00000006   PAD
    0x00000140   0x00000140   0x00000002   Code   RO          496    !!handler_null      c_p.l(__scatter.o)
    0x00000142   0x00000142   0x00000006   PAD
    0x00000148   0x00000148   0x0000001c   Code   RO          501    !!handler_zi        c_p.l(__scatter_zi.o)
    0x00000164   0x00000164   0x00000002   Code   RO          294    .ARM.Collect$$_printf_percent$$00000000  c_p.l(_printf_percent.o)
    0x00000166   0x00000166   0x0000000a   Code   RO          293    .ARM.Collect$$_printf_percent$$00000009  c_p.l(_printf_d.o)
    0x00000170   0x00000170   0x00000004   Code   RO          320    .ARM.Collect$$_printf_percent$$00000017  c_p.l(_printf_percent_end.o)
    0x00000174   0x00000174   0x00000002   Code   RO          356    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x00000176   0x00000176   0x00000000   Code   RO          370    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          372    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          374    .ARM.Collect$$libinit$$00000006  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          377    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          379    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          381    .ARM.Collect$$libinit$$00000010  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          384    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          386    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          388    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          390    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          392    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          394    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          396    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          398    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          400    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          402    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          404    .ARM.Collect$$libinit$$00000027  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          408    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          410    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          412    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000000   Code   RO          414    .ARM.Collect$$libinit$$00000034  c_p.l(libinit2.o)
    0x00000176   0x00000176   0x00000002   Code   RO          415    .ARM.Collect$$libinit$$00000035  c_p.l(libinit2.o)
    0x00000178   0x00000178   0x00000002   Code   RO          450    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          478    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          480    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          483    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          486    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          488    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000000   Code   RO          491    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0000017a   0x0000017a   0x00000002   Code   RO          492    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x0000017c   0x0000017c   0x00000000   Code   RO          309    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x0000017c   0x0000017c   0x00000000   Code   RO          324    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x0000017c   0x0000017c   0x00000006   Code   RO          336    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x00000182   0x00000182   0x00000000   Code   RO          326    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x00000182   0x00000182   0x00000004   Code   RO          327    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x00000186   0x00000186   0x00000000   Code   RO          329    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x00000186   0x00000186   0x00000008   Code   RO          330    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x0000018e   0x0000018e   0x00000002   Code   RO          361    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x00000190   0x00000190   0x00000000   Code   RO          421    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x00000190   0x00000190   0x00000004   Code   RO          422    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x00000194   0x00000194   0x00000006   Code   RO          423    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0000019a   0x0000019a   0x00000002   PAD
    0x0000019c   0x0000019c   0x00000030   Code   RO           19    .text               startup_mspm0g350x_uvision.o
    0x000001cc   0x000001cc   0x00000028   Code   RO          267    .text               c_p.l(noretval__2sprintf.o)
    0x000001f4   0x000001f4   0x0000006c   Code   RO          269    .text               c_p.l(__printf.o)
    0x00000260   0x00000260   0x0000006c   Code   RO          271    .text               c_p.l(_printf_dec.o)
    0x000002cc   0x000002cc   0x00000040   Code   RO          295    .text               c_p.l(rt_memclr.o)
    0x0000030c   0x0000030c   0x00000006   Code   RO          305    .text               c_p.l(heapauxi.o)
    0x00000312   0x00000312   0x000000b0   Code   RO          314    .text               c_p.l(_printf_intcommon.o)
    0x000003c2   0x000003c2   0x00000002   PAD
    0x000003c4   0x000003c4   0x00000030   Code   RO          316    .text               c_p.l(_printf_char_common.o)
    0x000003f4   0x000003f4   0x0000000a   Code   RO          318    .text               c_p.l(_sputc.o)
    0x000003fe   0x000003fe   0x00000028   Code   RO          321    .text               c_p.l(rtudiv10.o)
    0x00000426   0x00000426   0x0000003e   Code   RO          342    .text               c_p.l(sys_stackheap_outer.o)
    0x00000464   0x00000464   0x00000010   Code   RO          345    .text               c_p.l(exit.o)
    0x00000474   0x00000474   0x00000008   Code   RO          357    .text               c_p.l(libspace.o)
    0x0000047c   0x0000047c   0x0000000c   Code   RO          416    .text               c_p.l(sys_exit.o)
    0x00000488   0x00000488   0x00000002   Code   RO          439    .text               c_p.l(use_no_semi.o)
    0x0000048a   0x0000048a   0x00000000   Code   RO          441    .text               c_p.l(indicate_semi.o)
    0x0000048a   0x0000048a   0x00000002   PAD
    0x0000048c   0x0000048c   0x00000040   Code   RO          129    .text.DL_ADC12_setClockConfig  driverlib.a(dl_adc12.o)
    0x000004cc   0x000004cc   0x0000000a   Code   RO          141    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x000004d6   0x000004d6   0x00000002   PAD
    0x000004d8   0x000004d8   0x00000044   Code   RO          150    .text.DL_DMA_initChannel  driverlib.a(dl_dma.o)
    0x0000051c   0x0000051c   0x00000026   Code   RO          160    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x00000542   0x00000542   0x00000002   PAD
    0x00000544   0x00000544   0x000000c4   Code   RO          232    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000608   0x00000608   0x00000050   Code   RO          246    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000658   0x00000658   0x00000028   Code   RO          240    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000680   0x00000680   0x00000048   Code   RO          192    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x000006c8   0x000006c8   0x00000012   Code   RO          194    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x000006da   0x000006da   0x00000002   PAD
    0x000006dc   0x000006dc   0x000000e8   Code   RO          104    .text.IIC_ReadBytes  software_iic.o
    0x000007c4   0x000007c4   0x00000114   Code   RO          100    .text.IIC_RecvByte  software_iic.o
    0x000008d8   0x000008d8   0x00000134   Code   RO           98    .text.IIC_SendByte  software_iic.o
    0x00000a0c   0x00000a0c   0x00000094   Code   RO          108    .text.IIC_WriteBytes  software_iic.o
    0x00000aa0   0x00000aa0   0x0000001e   Code   RO          110    .text.Ping          software_iic.o
    0x00000abe   0x00000abe   0x00000002   PAD
    0x00000ac0   0x00000ac0   0x00000050   Code   RO           40    .text.SYSCFG_DL_ADC1_init  ti_msp_dl_config.o
    0x00000b10   0x00000b10   0x00000018   Code   RO           46    .text.SYSCFG_DL_DMA_CH0_init  ti_msp_dl_config.o
    0x00000b28   0x00000b28   0x00000008   Code   RO           42    .text.SYSCFG_DL_DMA_init  ti_msp_dl_config.o
    0x00000b30   0x00000b30   0x00000074   Code   RO           30    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00000ba4   0x00000ba4   0x00000054   Code   RO           34    .text.SYSCFG_DL_I2C_0_init  ti_msp_dl_config.o
    0x00000bf8   0x00000bf8   0x00000090   Code   RO           32    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00000c88   0x00000c88   0x0000001c   Code   RO           44    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00000ca4   0x00000ca4   0x0000005c   Code   RO           36    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00000d00   0x00000d00   0x00000074   Code   RO           38    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x00000d74   0x00000d74   0x00000028   Code   RO           26    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00000d9c   0x00000d9c   0x00000054   Code   RO           28    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00000df0   0x00000df0   0x00000044   Code   RO           66    .text.delay_ms      time.o
    0x00000e34   0x00000e34   0x00000044   Code   RO           64    .text.delay_us      time.o
    0x00000e78   0x00000e78   0x000000b8   Code   RO          118    .text.i2c_reset     software_iic.o
    0x00000f30   0x00000f30   0x00000238   Code   RO            2    .text.main          gpio_toggle_output.o
    0x00001168   0x00001168   0x00000038   Code   RO           78    .text.uart0_send_string  uart.o
    0x000011a0   0x000011a0   0x00000008   Data   RO           54    .rodata.gADC1ClockConfig  ti_msp_dl_config.o
    0x000011a8   0x000011a8   0x00000018   Data   RO           55    .rodata.gDMA_CH0Config  ti_msp_dl_config.o
    0x000011c0   0x000011c0   0x00000002   Data   RO           49    .rodata.gI2C_0ClockConfig  ti_msp_dl_config.o
    0x000011c2   0x000011c2   0x00000002   PAD
    0x000011c4   0x000011c4   0x00000028   Data   RO           48    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x000011ec   0x000011ec   0x00000002   Data   RO           50    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x000011ee   0x000011ee   0x0000000a   Data   RO           51    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x000011f8   0x000011f8   0x00000002   Data   RO           52    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x000011fa   0x000011fa   0x0000000a   Data   RO           53    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x00001204   0x00001204   0x00000021   Data   RO            9    .rodata.str1.1      gpio_toggle_output.o
    0x00001225   0x00001225   0x00000003   PAD
    0x00001228   0x00001228   0x00000020   Data   RO          494    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00001248, Size: 0x00002188, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00001248   0x00000008   Data   RW          120    .data.reset_magic_number  software_iic.o
    0x20200008        -       0x00000060   Zero   RW          358    .bss                c_p.l(libspace.o)
    0x20200068        -       0x00000008   Zero   RW            6    .bss.Anolog         gpio_toggle_output.o
    0x20200070        -       0x00000001   Zero   RW            8    .bss.Digtal         gpio_toggle_output.o
    0x20200071        -       0x0000000a   Zero   RW            5    .bss.IIC_write_buff  gpio_toggle_output.o
    0x2020007b        -       0x00000008   Zero   RW            7    .bss.Normal         gpio_toggle_output.o
    0x20200083   0x00001250   0x00000001   PAD
    0x20200084        -       0x00000100   Zero   RW            4    .bss.rx_buff        gpio_toggle_output.o
    0x20200184   0x00001250   0x00000004   PAD
    0x20200188        -       0x00001000   Zero   RW           17    HEAP                startup_mspm0g350x_uvision.o
    0x20201188        -       0x00001000   Zero   RW           16    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       568        156         33          0        283       1175   gpio_toggle_output.o
      1178         24          0          8          0      18202   software_iic.o
        48         22        192          0       8192        732   startup_mspm0g350x_uvision.o
       816        156         98          0          0      36122   ti_msp_dl_config.o
       136          8          0          0          0       1917   time.o
        56          4          0          0          0       3389   uart.o

    ----------------------------------------------------------------------
      2804        <USER>        <GROUP>          8       8480      61537   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          5          0          5          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       108          0          0          0          0         76   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        86         10          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         88   _printf_char_common.o
        10          0          0          0          0          0   _printf_d.o
       108         18          0          0          0         76   _printf_dec.o
       176          0          0          0          0         84   _printf_intcommon.o
         2          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         60   _sputc.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        40          4          0          0          0         84   noretval__2sprintf.o
        64          0          0          0          0        108   rt_memclr.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        40          0          0          0          0         60   rtudiv10.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        64          8          0          0          0       4747   dl_adc12.o
        10          0          0          0          0        802   dl_common.o
        68          4          0          0          0       4504   dl_dma.o
        38          0          0          0          0       8589   dl_i2c.o
       316         28          0          0          0      18490   dl_sysctl_mspm0g1x0x_g3x0x.o
        90          8          0          0          0      14127   dl_uart.o

    ----------------------------------------------------------------------
      1516         <USER>          <GROUP>          0         96      52443   Library Totals
        28          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       902         46          0          0         96       1184   c_p.l
       586         48          0          0          0      51259   driverlib.a

    ----------------------------------------------------------------------
      1516         <USER>          <GROUP>          0         96      52443   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      4320        468        360          8       8576     113404   Grand Totals
      4320        468        360          8       8576     113404   ELF Image Totals
      4320        468        360          8          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 4680 (   4.57kB)
    Total RW  Size (RW Data + ZI Data)              8584 (   8.38kB)
    Total ROM Size (Code + RO Data + RW Data)       4688 (   4.58kB)

==============================================================================

