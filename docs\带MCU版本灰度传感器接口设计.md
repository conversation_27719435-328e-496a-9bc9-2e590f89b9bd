# 带MCU版本灰度传感器接口设计

## 📋 设计目标

1. **统一接口**：与无MCU版本保持相似的API风格
2. **简化使用**：隐藏I2C通信细节
3. **高效性能**：直接读取传感器处理好的数据
4. **易于移植**：最小化依赖文件

## 🎯 核心接口设计

### 文件结构
```
User/
├── gw_mcu_grayscale_sensor.h    // 带MCU版本接口头文件
├── gw_mcu_grayscale_sensor.c    // 带MCU版本接口实现
├── software_iic.h               // I2C通信层
├── software_iic.c               // I2C通信层
└── gw_grayscale_sensor.h        // 寄存器定义
```

## 🔧 接口函数设计

### 1. 初始化接口
```c
// 传感器初始化
uint8_t MCU_Grayscale_Init(void);

// 传感器连接检测
uint8_t MCU_Grayscale_Ping(void);

// 传感器重置
void MCU_Grayscale_Reset(void);
```

### 2. 数据获取接口
```c
// 获取8位数字量（主要循迹接口）
uint8_t MCU_Grayscale_GetDigital(void);

// 获取8通道模拟量
uint8_t MCU_Grayscale_GetAnalog(uint16_t* analog_values);

// 获取归一化数据（PID控制用）
uint8_t MCU_Grayscale_GetNormalized(uint16_t* normalized_values);

// 获取单个通道数据
uint16_t MCU_Grayscale_GetChannel(uint8_t channel);
```

### 3. 配置接口
```c
// 设置传感器地址
uint8_t MCU_Grayscale_SetAddress(uint8_t new_addr);

// 设置校准参数
uint8_t MCU_Grayscale_SetCalibration(uint16_t* white_values, uint16_t* black_values);

// 启用/禁用特定通道
uint8_t MCU_Grayscale_EnableChannels(uint8_t channel_mask);
```

### 4. 状态查询接口
```c
// 获取传感器状态
uint8_t MCU_Grayscale_GetStatus(void);

// 获取错误信息
uint8_t MCU_Grayscale_GetError(void);

// 获取固件版本
uint8_t MCU_Grayscale_GetFirmware(void);
```

## 📊 数据结构设计

### 传感器数据结构
```c
typedef struct {
    uint8_t  digital_data;        // 8位数字量
    uint16_t analog_data[8];      // 8通道模拟量
    uint16_t normalized_data[8];  // 8通道归一化数据
    uint8_t  sensor_status;       // 传感器状态
    uint8_t  error_code;          // 错误代码
    uint8_t  firmware_version;    // 固件版本
    uint8_t  is_connected;        // 连接状态
} MCU_Grayscale_Data_t;
```

### 配置结构
```c
typedef struct {
    uint8_t  sensor_address;      // 传感器I2C地址
    uint16_t white_calibration[8]; // 白色校准值
    uint16_t black_calibration[8]; // 黑色校准值
    uint8_t  enabled_channels;    // 启用的通道掩码
    uint8_t  sample_rate;         // 采样率配置
} MCU_Grayscale_Config_t;
```

## 🎮 使用示例

### 基本循迹示例
```c
#include "gw_mcu_grayscale_sensor.h"

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 传感器初始化
    if(MCU_Grayscale_Init() != 0) {
        printf("Sensor init failed!\n");
        return -1;
    }
    
    // 主循环
    while(1) {
        // 获取数字量数据
        uint8_t digital_line = MCU_Grayscale_GetDigital();
        
        // 循迹控制
        line_following_control(digital_line);
        
        delay_ms(10);
    }
}
```

### 高精度PID控制示例
```c
void advanced_line_following(void)
{
    uint16_t analog_values[8];
    
    // 获取模拟量数据
    if(MCU_Grayscale_GetAnalog(analog_values) == 0) {
        // 计算加权位置
        float line_position = calculate_weighted_position(analog_values);
        
        // PID控制
        float pid_output = pid_calculate(line_position);
        
        // 电机控制
        motor_control(pid_output);
    }
}
```

## 🔍 错误处理设计

### 错误代码定义
```c
#define MCU_GRAYSCALE_OK            0x00  // 正常
#define MCU_GRAYSCALE_ERROR_COMM    0x01  // 通信错误
#define MCU_GRAYSCALE_ERROR_INIT    0x02  // 初始化错误
#define MCU_GRAYSCALE_ERROR_CALIB   0x03  // 校准错误
#define MCU_GRAYSCALE_ERROR_TIMEOUT 0x04  // 超时错误
```

### 错误处理机制
```c
uint8_t error_code = MCU_Grayscale_GetDigital();
if(error_code == 0xFF) {
    // 处理通信错误
    uint8_t status = MCU_Grayscale_GetStatus();
    if(status & MCU_GRAYSCALE_ERROR_COMM) {
        // 重新初始化
        MCU_Grayscale_Reset();
        MCU_Grayscale_Init();
    }
}
```
